<template>
    <div class="portal-container">
        <div class="router-view-container">
            <RouterView />
        </div>
        <!-- 返回家家精灵按钮 - 只有从家家精灵跳转过来才显示 -->
        <BackToGenie v-if="isFromGenie" />
        <nav class="portal-nav">
            <RouterLink to="/smart-trainer/practice/home" custom v-slot="{ isActive, navigate }">
                <div :class="['nav-item', { active: isActive }]" @click="navigate">
                    <i class="pi pi-home text-lg"></i>
                    <span>首页</span>
                </div>
            </RouterLink>
            <RouterLink to="/smart-trainer/practice/rank" custom v-slot="{ isActive, navigate }">
                <div :class="['nav-item', { active: isActive }]" @click="navigate">
                    <i class="pi pi-chart-bar text-lg"></i>
                    <span>排行榜</span>
                </div>
            </RouterLink>
            <RouterLink to="/smart-trainer/practice/my" custom v-slot="{ isActive, navigate }">
                <div :class="['nav-item', { active: isActive }]" @click="navigate">
                    <i class="pi pi-user text-lg"></i>
                    <span>我的</span>
                </div>
            </RouterLink>
        </nav>
    </div>
</template>

<script setup>
import { computed, onMounted } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import BackToGenie from '../../components/BackToGenie.vue';

const router = useRouter();
const route = useRoute();

// 检查是否从家家精灵跳转过来
const isFromGenie = computed(() => {
    return Number(route.query.fromGenie) === 1;
});

onMounted(() => {
    // 如果访问 /practice 路径，重定向到 home
    if (router.currentRoute.value.path === '/smart-trainer/practice') {
        router.replace('/smart-trainer/practice/home');
    }
});
</script>

<style lang="scss" scoped>
.portal-container {
    height: 100vh;
    background-color: #f4f7fc;
    display: flex;
    flex-direction: column;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    overflow: hidden;
    font-weight: 500;
}

.portal-nav {
    display: flex;
    justify-content: space-around;
    align-items: center;
    background: $system-background-primary;
    padding-top: 8px;
    padding-bottom: calc(env(safe-area-inset-bottom));
    position: relative;
    margin-top: auto;
    left: 0;
    right: 0;
    z-index: 10;
    border-top: 1px solid $fill-color-quaternary;
    min-height: 60px;

    .nav-item {
        flex: 1;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 4px 8px;
        gap: 2px;
        cursor: pointer;
        transition: all 0.3s ease;
        position: relative;
        height: 100%;

        i {
            font-size: 18px;
            color: $label-tertiary;
            transition: color 0.3s ease, transform 0.2s ease;
        }

        span {
            font-size: 12px;
            color: $label-tertiary;
            font-weight: 400;
            transition: color 0.3s ease;
            margin-top: 2px;
        }

        &.active {
            i,
            span {
                color: $system-blue;
                font-weight: 500;
            }

            i {
                transform: translateY(-1px);
            }
        }

        &:active {
            transform: scale(0.95);
            opacity: 0.9;
        }
    }
}

.router-view-container {
    flex: 1;
    overflow-y: auto;
}
</style>
