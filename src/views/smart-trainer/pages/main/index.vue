<template>
    <div class="trainer-main">
        <!-- 顶部搜索栏 -->
        <div class="header-section">
            <div class="search-container">
                <div class="search-box">
                    <i class="pi pi-search search-icon"></i>
                    <input
                        type="text"
                        placeholder="查询课程"
                        v-model="searchText"
                        class="search-input"
                    />
                </div>
                <div class="message-icon" @click="handleMessageClick">
                    <i class="pi pi-envelope"></i>
                    <span class="message-dot" v-if="hasNewMessage"></span>
                </div>
            </div>
        </div>

        <!-- 导航栏 -->
        <div class="navigation-section">
            <div class="nav-tabs">
                <div
                    v-for="tab in navTabs"
                    :key="tab.key"
                    class="nav-tab"
                    :class="{ active: activeTab === tab.key }"
                    @click="handleTabChange(tab.key)"
                >
                    <span class="tab-text">{{ tab.label }}</span>
                    <div class="tab-underline" v-if="activeTab === tab.key"></div>
                </div>
            </div>
        </div>

        <!-- 主要内容区域 -->
        <div class="content-section">
            <!-- 轮播图区域 -->
            <div class="banner-section">
                <swiper
                    :modules="[Autoplay, Pagination, EffectFade]"
                    :slides-per-view="1"
                    :space-between="0"
                    :loop="true"
                    :autoplay="{
                        delay: 3000,
                        disableOnInteraction: false
                    }"
                    :pagination="{ clickable: true }"
                    :effect="'fade'"
                    class="swiper-container"
                >
                    <swiper-slide v-for="(banner, index) in banners" :key="index">
                        <img :src="banner.image" :alt="banner.title" class="banner-image" />
                        <div class="banner-content">
                            <h3 class="banner-title">{{ banner.title }}</h3>
                            <p class="banner-subtitle">{{ banner.subtitle }}</p>
                            <div class="banner-tag" v-if="banner.tag">
                                <span class="tag-text">{{ banner.tag }}</span>
                                <span class="tag-hot">🔥</span>
                            </div>
                        </div>
                    </swiper-slide>
                </swiper>
            </div>

            <!-- 课程分类卡片 -->
            <div class="course-category-section">
                <div class="category-cards">
                    <div
                        v-for="category in courseCategories"
                        :key="category.key"
                        class="category-card"
                        :style="{ backgroundColor: category.backgroundColor }"
                        @click="handleCategoryClick(category.key)"
                    >
                        <!-- 顶部大黑字标题 -->
                        <div class="card-main-title">{{ category.title }}</div>

                        <!-- 带颜色的介绍 -->
                        <p class="card-colored-intro" :style="{ color: category.introColor }">
                            {{ category.subtitle }}
                        </p>

                        <!-- 主要内容区域 -->
                        <div class="card-main-content">
                            <ul class="feature-list">
                                <li
                                    v-for="feature in category.features"
                                    :key="feature"
                                    class="feature-item"
                                >
                                    • {{ feature }}
                                </li>
                            </ul>
                        </div>

                        <!-- 右侧图标 -->
                        <div class="card-icon">
                            <img
                                :src="category.image"
                                :alt="category.title"
                                class="category-image"
                            />
                        </div>
                    </div>
                </div>
            </div>

            <!-- 根据当前激活的标签显示不同内容 -->
            <div class="tab-content">
                <div v-if="activeTab === 'recommend'" class="recommend-content">
                    <h3>推荐内容</h3>
                    <p>这里显示推荐的课程内容</p>
                </div>
                <div v-else-if="activeTab === 'free'" class="free-content">
                    <h3>免费课程</h3>
                    <p>这里显示免费课程列表</p>
                </div>
                <div v-else-if="activeTab === 'practice'" class="practice-content">
                    <h3>实战课程</h3>
                    <p>这里显示实战课程列表</p>
                </div>
                <div v-else-if="activeTab === 'system'" class="system-content">
                    <h3>体系课程</h3>
                    <p>这里显示体系课程列表</p>
                </div>
                <div v-else-if="activeTab === 'column'" class="column-content">
                    <h3>专栏内容</h3>
                    <p>这里显示专栏列表</p>
                </div>
                <div v-else-if="activeTab === 'tutorial'" class="tutorial-content">
                    <h3>教程内容</h3>
                    <p>这里显示教程列表</p>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue';
import { useRouter } from 'vue-router';
// 引入 Swiper
import { Swiper, SwiperSlide } from 'swiper/vue';
import { Autoplay, Pagination, EffectFade } from 'swiper/modules';

// 引入 Swiper 样式
import 'swiper/css';
import 'swiper/css/pagination';
import 'swiper/css/effect-fade';

const router = useRouter();

// 响应式状态
const searchText = ref('');
const hasNewMessage = ref(true);
const activeTab = ref('recommend');

// 导航标签配置
const navTabs = ref([
    { key: 'recommend', label: '推荐' },
    { key: 'free', label: '免费课' },
    { key: 'practice', label: '实战课' },
    { key: 'system', label: '体系课' },
    { key: 'column', label: '专栏' },
    { key: 'tutorial', label: '教程' }
]);

// 轮播图数据
const banners = ref([
    {
        image: 'https://images.unsplash.com/photo-1516321318423-f06f85e504b3?w=800&h=400&fit=crop',
        title: 'AI Agent+MCP从0到1',
        subtitle: '打造商业级编程智能体',
        tag: '上新特惠'
    },
    {
        image: 'https://images.unsplash.com/photo-1498050108023-c5249f4df085?w=800&h=400&fit=crop',
        title: '全栈开发训练营',
        subtitle: '从前端到后端一站式学习',
        tag: '限时优惠'
    }
]);

// 课程分类数据
const courseCategories = ref([
    {
        key: 'study',
        title: '练',
        subtitle: '练就过硬本领',
        introColor: '#E93E43',
        features: ['语音对练', 'AI 评分'],
        image: 'https://z.autoimg.cn/dealer_microfe_aidev/assets/chat-knowledge-h5/smart-trainer/main/practice.png',
        backgroundColor: '#fdebee'
    },
    {
        key: 'practice',
        title: '考',
        subtitle: '检验练习成果',
        introColor: '#FCA43D',
        features: ['真实项目', 'AI 批改'],
        image: 'https://z.autoimg.cn/dealer_microfe_aidev/assets/chat-knowledge-h5/smart-trainer/main/access.png',
        backgroundColor: '#fff9f1'
    }
]);

// 计算属性
const computedValue = computed(() => {
    return searchText.value;
});

/**
 * 处理消息图标点击
 */
const handleMessageClick = () => {
    console.log('打开消息页面');
    // 这里可以路由到消息页面
};

/**
 * 处理标签切换
 * @param {string} tabKey - 标签键值
 */
const handleTabChange = tabKey => {
    activeTab.value = tabKey;
    console.log(`切换到标签: ${tabKey}`);
};

/**
 * 处理课程分类点击
 * @param {string} categoryKey - 分类键值
 */
const handleCategoryClick = categoryKey => {
    console.log(`点击了课程分类: ${categoryKey}`);
    // 这里可以路由到对应的课程列表页面
};

// 生命周期
onMounted(() => {
    // 组件挂载后的逻辑
});

onUnmounted(() => {
    // 清理定时器等资源
});
</script>

<style lang="scss" scoped>
.trainer-main {
    width: 100%;
    height: 100%;

    // 顶部搜索栏
    .header-section {
        background: #fff;
        padding: 12px 16px 6px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

        .search-container {
            display: flex;
            align-items: center;
            gap: 12px;

            .search-box {
                flex: 1;
                position: relative;
                display: flex;
                align-items: center;
                background: #f8f9fa;
                border-radius: 20px;
                padding: 8px 16px;

                .search-icon {
                    color: #999;
                    margin-right: 8px;
                }

                .search-input {
                    flex: 1;
                    border: none;
                    background: transparent;
                    outline: none;
                    font-size: 14px;
                    color: #333;

                    &::placeholder {
                        color: #999;
                    }
                }
            }

            .message-icon {
                position: relative;
                width: 40px;
                height: 40px;
                display: flex;
                align-items: center;
                justify-content: center;
                cursor: pointer;

                i {
                    font-size: 20px;
                    color: #666;
                }

                .message-dot {
                    position: absolute;
                    top: 8px;
                    right: 8px;
                    width: 8px;
                    height: 8px;
                    background: #ff4757;
                    border-radius: 50%;
                }
            }
        }
    }

    // 导航栏
    .navigation-section {
        background: #fff;
        padding: 0 16px;

        .nav-tabs {
            display: flex;
            align-items: center;
            overflow-x: auto;
            gap: 24px;

            .nav-tab {
                position: relative;
                padding: 12px 0;
                cursor: pointer;
                white-space: nowrap;
                transition: color 0.3s ease;

                .tab-text {
                    font-size: 16px;
                    font-weight: 500;
                    color: #000;
                    transition: color 0.3s ease;
                }

                .tab-underline {
                    position: absolute;
                    bottom: 0;
                    left: 50%;
                    transform: translateX(-50%);
                    height: 4px;
                    width: 30%;
                    background: #ff4757;
                    border-radius: 4px;
                }

                &.active {
                    .tab-text {
                        color: #ff4757;
                        font-weight: 600;
                    }
                }
            }
        }
    }

    // 主要内容区域
    .content-section {
        padding: 16px;

        // 轮播图区域
        .banner-section {
            margin-bottom: 10px;

            .swiper-container {
                position: relative;
                border-radius: 6px;
                overflow: hidden;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
                // 调整高度
                height: 120px;

                .swiper-slide {
                    display: flex;
                    align-items: center;
                    justify-content: center;

                    .banner-image {
                        width: 100%;
                        height: 100%;
                        object-fit: cover;
                    }

                    .banner-content {
                        position: absolute;
                        top: 0;
                        left: 0;
                        right: 0;
                        bottom: 0;
                        background: linear-gradient(
                            135deg,
                            rgba(138, 43, 226, 0.8),
                            rgba(147, 51, 234, 0.6)
                        );
                        display: flex;
                        flex-direction: column;
                        justify-content: center;
                        padding: 24px;
                        color: white;

                        .banner-title {
                            font-size: 20px;
                            font-weight: 600;
                            margin-bottom: 8px;
                        }

                        .banner-subtitle {
                            font-size: 14px;
                            opacity: 0.9;
                            margin-bottom: 16px;
                        }

                        .banner-tag {
                            display: flex;
                            align-items: center;
                            gap: 4px;
                            background: rgba(255, 255, 255, 0.2);
                            padding: 4px 12px;
                            border-radius: 16px;
                            width: fit-content;

                            .tag-text {
                                font-size: 12px;
                                font-weight: 500;
                            }
                        }
                    }
                }

                // 覆盖 Swiper 分页器样式
                :deep(.swiper-pagination-bullet) {
                    width: 8px;
                    height: 8px;
                    background: rgba(255, 255, 255, 0.7);
                    opacity: 1;
                    transition: all 0.3s ease;
                }

                :deep(.swiper-pagination-bullet-active) {
                    width: 20px;
                    border-radius: 4px;
                    background: white;
                }
            }
        }

        // 课程分类卡片
        .course-category-section {
            margin-bottom: 24px;

            .category-cards {
                display: flex;
                gap: 12px;

                .category-card {
                    position: relative;
                    flex: 1;
                    border-radius: 12px;
                    padding: 10px 14px 4px;
                    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
                    cursor: pointer;
                    transition: all 0.3s ease;
                    overflow: hidden;

                    &:hover {
                        transform: translateY(-2px);
                        box-shadow: 0 6px 16px rgba(0, 0, 0, 0.08);
                    }

                    .card-main-title {
                        font-size: 18px;
                        font-weight: 600;
                        color: #333;
                        margin-bottom: 2px;
                    }

                    .card-colored-intro {
                        font-size: 14px;
                        font-weight: 500;
                        margin-bottom: 6px;
                    }

                    .card-main-content {
                        .feature-list {
                            list-style: none;
                            padding: 0;
                            margin: 0;

                            .feature-item {
                                font-size: 12px;
                                color: #666;
                                line-height: 1.5;
                                margin-bottom: 4px;
                            }
                        }
                    }

                    .card-icon {
                        position: absolute;
                        bottom: 4px;
                        right: 4px;
                        width: 50px;
                        height: 50px;

                        .category-image {
                            width: 100%;
                            height: 100%;
                            object-fit: contain;
                        }
                    }
                }
            }
        }

        // 标签内容区域
        .tab-content {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

            h3 {
                font-size: 16px;
                font-weight: 600;
                color: #333;
                margin-bottom: 12px;
            }

            p {
                font-size: 14px;
                color: #666;
                line-height: 1.5;
            }
        }
    }
}
</style>
