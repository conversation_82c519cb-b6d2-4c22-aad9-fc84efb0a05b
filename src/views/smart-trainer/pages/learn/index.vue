<template>
    <div class="learn-container">
        <div class="learn-header">
            <h1>学习模块</h1>
            <p>知识学习与理论掌握</p>
        </div>
        <div class="router-view-container">
            <RouterView />
        </div>
    </div>
</template>

<script setup>
import { onMounted } from 'vue';

onMounted(() => {
    console.log('学习模块已加载');
});
</script>

<style lang="scss" scoped>
.learn-container {
    height: 100vh;
    background-color: #f4f7fc;
    display: flex;
    flex-direction: column;
}

.learn-header {
    padding: 20px 16px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    text-align: center;

    h1 {
        font-size: 24px;
        font-weight: 600;
        margin-bottom: 8px;
    }

    p {
        font-size: 14px;
        opacity: 0.9;
    }
}

.router-view-container {
    flex: 1;
    overflow-y: auto;
}
</style>
