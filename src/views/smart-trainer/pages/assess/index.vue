<template>
    <div class="assess-container">
        <div class="assess-header">
            <h1>考核模块</h1>
            <p>能力评估与考试测评</p>
        </div>
        <div class="router-view-container">
            <RouterView />
        </div>
    </div>
</template>

<script setup>
import { onMounted } from 'vue';

onMounted(() => {
    console.log('考核模块已加载');
});
</script>

<style lang="scss" scoped>
.assess-container {
    height: 100vh;
    background-color: #f4f7fc;
    display: flex;
    flex-direction: column;
}

.assess-header {
    padding: 20px 16px;
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
    color: white;
    text-align: center;

    h1 {
        font-size: 24px;
        font-weight: 600;
        margin-bottom: 8px;
    }

    p {
        font-size: 14px;
        opacity: 0.9;
    }
}

.router-view-container {
    flex: 1;
    overflow-y: auto;
}
</style>
